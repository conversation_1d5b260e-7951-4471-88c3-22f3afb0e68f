// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:tts_truong_lai/value_notifier_list.dart';
import 'package:tts_truong_lai/theme/app_theme.dart';
import 'package:tts_truong_lai/widgets/custom_widgets.dart';
enum TtsState { playing, stopped, paused, continued }
class TTSTruongLaiBody extends StatefulWidget {
  const TTSTruongLaiBody({super.key, required this.total});
  final int total;
  @override
  State<TTSTruongLaiBody> createState() => _TTSTruongLaiBodyState();
}

class _TTSTruongLaiBodyState extends State<TTSTruongLaiBody> with TickerProviderStateMixin {
  late TextEditingController controller;
  late FlutterTts flutterTts;
  late FocusNode _focusNode;
  late AnimationController _removeAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  String? _removingStudentId;

  // Lưu reference của keyboard handler để có thể remove sau này
  late bool Function(KeyEvent) _keyboardHandler;
   String? language;
  String? engine;
  double volume = 1;
  double pitch = 1.0;
  double rate = 0.5;
  bool isCurrentLanguageInstalled = false;
  ValueNotifier<TtsState?> vState = ValueNotifier(null);


  TtsState ttsState = TtsState.stopped;

  bool get isPlaying => ttsState == TtsState.playing;
  bool get isStopped => ttsState == TtsState.stopped;
  bool get isPaused => ttsState == TtsState.paused;
  bool get isContinued => ttsState == TtsState.continued;

  bool get isIOS => !kIsWeb && Platform.isIOS;
  bool get isAndroid => !kIsWeb && Platform.isAndroid;
  bool get isWindows => !kIsWeb && Platform.isWindows;
  bool get isWeb => kIsWeb;
  @override
  void initState() {
    _createStudentList(widget.total);
    initTts();
    _loadSettings();
    _scrollController = ScrollController();
    controller = TextEditingController(text: '');
    _focusNode = FocusNode();

    // Tạo và lưu reference của keyboard handler
    _keyboardHandler = (event) {
      // Chỉ xử lý KeyDownEvent để tránh duplicate calls
      if (event is! KeyDownEvent) return false;

      if (event.logicalKey == LogicalKeyboardKey.keyX) {
        _removeSelectedStudent();
        return true;
      }
      if (event.logicalKey == LogicalKeyboardKey.keyV) {
        if (selectedStudents.value != null) {
          _markStudentAbsent(selectedStudents.value!);
        } else {
        }
        return true;
      }
      return false;
    };

    // Thêm handler vào HardwareKeyboard
    HardwareKeyboard.instance.addHandler(_keyboardHandler);
    // Khởi tạo animation controller
    _removeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _removeAnimationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(1.0, 0.0),
    ).animate(CurvedAnimation(
      parent: _removeAnimationController,
      curve: Curves.easeInOut,
    ));

    super.initState();
  }

  @override
  void dispose() {
    // Remove keyboard handler để tránh memory leak và duplicate calls
    HardwareKeyboard.instance.removeHandler(_keyboardHandler);

    _scrollController.dispose();
    controller.dispose();
    _focusNode.dispose();
    _removeAnimationController.dispose();
    flutterTts.stop();
    super.dispose();
  }
   dynamic initTts()async {
    flutterTts = FlutterTts();
  
    await flutterTts.setLanguage("vi-VN");

    _setAwaitOptions();

    if (isAndroid) {
      _getDefaultEngine();
      _getDefaultVoice();
    }

    flutterTts.setStartHandler(() {
      vState.value = TtsState.playing;
    });

    flutterTts.setCompletionHandler(() {
      vState.value = TtsState.stopped;
    });

    flutterTts.setCancelHandler(() {
        vState.value = TtsState.stopped;
    });

    flutterTts.setPauseHandler(() {
         vState.value = TtsState.paused;
    
    });

    flutterTts.setContinueHandler(() {
       vState.value = TtsState.continued;
   
    });

    flutterTts.setErrorHandler((msg) {
        vState.value = TtsState.stopped;
   
    });
  }
  Future<void> _setAwaitOptions() async {
    await flutterTts.awaitSpeakCompletion(true);
  }
  Future<void> _getDefaultEngine() async {
    var engine = await flutterTts.getDefaultEngine;
    if (engine != null) {
    }
  }

  Future<void> _getDefaultVoice() async {
    var voice = await flutterTts.getDefaultVoice;
    if (voice != null) {
    }
  }
   Future<void> _speak(final String text) async {
    await flutterTts.setVolume(volume);
    await flutterTts.setSpeechRate(rate);
    await flutterTts.setPitch(pitch);
    await flutterTts.speak(text);
   
     
  }
    Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
      rate = prefs.getDouble('slider1_value') ?? 0.81;
      pitch = prefs.getDouble('slider2_value') ?? 0.97;
  }
  ValueNotifierList<_Persion> students = ValueNotifierList([]);
  ValueNotifierList<_Persion> studentsAfter = ValueNotifierList([]);
  ValueNotifier<_Persion?> selectedStudents = ValueNotifier(null);
  int indexSelect = 0;
  late ScrollController _scrollController;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          'Hệ thống quản lý thi',
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: AppTheme.cardColor,
        elevation: 0,
      ),
      body:  _buildStudentWaitingPanel(context)
    );
  }

  Future<void> _createStudentList(int total)async {
    final prefs = await SharedPreferences.getInstance();
    final text1 = prefs.getString('textfield1_value') ?? 'Xin mời thí sinh';
    final text2 = prefs.getString('textfield2_value') ?? 'chuẩn bị';
    students.clear();
    for (var i = 1; i <= total; i++) {
      final sbd = i.toString();
     // .padLeft(total.toString().length, '0');
      // StringBuffer buffer = StringBuffer();
      // for (int j = 0; j < sbd.length; j++) {
      //   if (j > 0) buffer.write(' ');
      //   buffer.write(sbd[j]);
      // }
     // String result = buffer.toString();
      final person = _Persion(sbd, '$text1 $sbd $text2', false, false);
      students.add(person);
    }
  }

  Widget _buildStudentWaitingPanel(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ModernCard(
            padding: const EdgeInsets.all(0),
            child: Column(
              children: [
                SectionHeader(
                  title: 'Danh sách chờ thi',
                  subtitle: 'Thí sinh đang chờ được gọi thi',
                  action: Row(
                    spacing: 8,
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 200,
                        child: TextField(
                          onSubmitted: (value) {
                              try {
                                  final student = students.value.firstWhere(
                                    (element) => element.id == controller.text,
                                  );
                                  // Found student, scroll to it
                                  selectedStudents.value = student;
                                  _scrollToStudent(student);
                                  _speak(student.text);
                                } catch (e) {
                                  // Student not found
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Không tìm thấy thí sinh có số báo danh: ${controller.text}'),
                                      backgroundColor: AppTheme.errorColor,
                                    ),
                                  );
                                }
                          },
                          controller: controller,
                          decoration: InputDecoration(
                            labelText: 'Tìm kiếm',
                            hintText: 'Nhập số báo danh...',
                            
                            suffixIcon: IconButton(
                              onPressed: (){
                                try {
                                  final student = students.value.firstWhere(
                                    (element) => element.id == controller.text,
                                  );
                                  // Found student, scroll to it
                                  selectedStudents.value = student;
                                  _scrollToStudent(student);
                                  _speak(student.text);
                                } catch (e) {
                                  // Student not found
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text('Không tìm thấy thí sinh có số báo danh: ${controller.text}'),
                                      backgroundColor: AppTheme.errorColor,
                                    ),
                                  );
                                }
                              },
                              icon: Icon(Icons.search)),
                          ),
                        ),
                      ),
                      ValueListenableBuilder(
                        valueListenable: students,
                        builder: (_, vStudents, __) => StatusBadge.info(
                          '${vStudents.length} thí sinh',
                          icon: Icons.people,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ValueListenableBuilder(
                    valueListenable: students,
                    builder: (_, vStudents, child) {
                      if (vStudents.isEmpty) {
                        return EmptyState(
                          icon: Icons.people_outline,
                          title: 'Chưa có thí sinh',
                          subtitle: 'Vui lòng thiết lập số lượng thí sinh ở trên',
                        );
                      }
          
                      return ValueListenableBuilder(
                        valueListenable: vState,
                        builder: (context, valState, child) {
                          return ValueListenableBuilder(
                            valueListenable: selectedStudents,
                            builder: (context, vSelectedStudent, child) {
                              return ListView.builder(
                                controller: _scrollController,
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                itemCount: vStudents.length,
                                physics: const BouncingScrollPhysics(),
                                itemExtent: 80, // Đặt chiều cao cố định cho mỗi item
                                itemBuilder: (_, i) {
                                  final item = vStudents[i];
                                  final isSelected = vSelectedStudent?.id == item.id;
                                  final isRemoving = _removingStudentId == item.id;

                                  Widget cardWidget = ModernCard(
                                    key: GlobalObjectKey(item),
                                    isSelected: isSelected,
                                    onTap: isSelected ? null : () {
                                      selectedStudents.value = item;
                                    },
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 40,
                                          height: 40,
                                          decoration: BoxDecoration(
                                            color: isSelected
                                                ? AppTheme.primaryColor
                                                : AppTheme.textTertiary.withValues(alpha: 0.1),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                          child: Center(
                                            child: Text(
                                              item.id,
                                              style: TextStyle(
                                                color: isSelected
                                                    ? Colors.white
                                                    : AppTheme.textSecondary,
                                                fontWeight: FontWeight.w600,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'Thí sinh ${item.id}',
                                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        if (item.isVang && !item.isDone)
                                          StatusBadge.warning('Vắng mặt')
                                        else if (!item.isVang && item.isDone)
                                          StatusBadge.success('Hoàn thành')
                                        else if (isSelected)
                                          Row(
                                            spacing: 8,
                                            children: [
                                            TextButton(
                                              style: TextButton.styleFrom(
                                                backgroundColor: AppTheme.warningColor.withValues(alpha: 0.1),
                                                foregroundColor: AppTheme.warningColor
                                                
                                              ),
                                              onPressed: (){
                                              _markStudentAbsent(item);
                                            }, child: Text('Vắng mặt')),
                                             TextButton(
                                              style: TextButton.styleFrom(
                                                backgroundColor: AppTheme.errorColor.withValues(alpha: 0.1),
                                                foregroundColor: AppTheme.errorColor
                                                
                                              ),
                                              onPressed: valState == TtsState.playing ? null:(){
                                                _speak(item.text);
                                            }, child: Text('Gọi lại')),
                                            
                                          ],)
                                        else
                                          SizedBox()
                                      ],
                                    ),
                                  );

                                  // Áp dụng animation nếu item đang được xóa
                                  if (isRemoving) {
                                    return AnimatedBuilder(
                                      animation: _removeAnimationController,
                                      builder: (context, child) {
                                        return SlideTransition(
                                          position: _slideAnimation,
                                          child: FadeTransition(
                                            opacity: _fadeAnimation,
                                            child: cardWidget,
                                          ),
                                        );
                                      },
                                    );
                                  }

                                  return cardWidget;
                                },
                              );
                            },
                          );
                        }
                      );
                    },
                  ),
                ),
                _buildControlButtons(context),
              ],
            ),
          ),
        ),
         Expanded(child: _buildStudentAfterPanel(context))
      ],
    );
  }

  Widget _buildControlButtons(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: vState,
      builder: (context, valueState, child) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.selectedColor.withValues(alpha: 0.3),
            border: Border(
              top: BorderSide(color: AppTheme.borderColor),
            ),
          ),
          child: valueState == TtsState.playing
          ? ModernButton(
              enabled: valueState == TtsState.playing,
              text: 'Đang gọi thí sinh...',)
          : ValueListenableBuilder(
            valueListenable: selectedStudents,
            builder: (_, vStudent, child) {
              return Row(
                children: [
                  Expanded(
                    child: vStudent == null
                        ? ModernButton(
                            text: 'Bắt đầu thi',
                            icon: Icons.play_arrow,
                            onPressed:  () {
                              _startExam();
                            } ,
                          )
                        : ModernButton(
                            text: 'Thí sinh tiếp theo',
                            icon: Icons.skip_next,
                            onPressed: _hasNextStudent() ? () {
                              _nextStudent();
                            } : null,
                          ),
                  ),
                ],
              );
            },
          ),
        );
      }
    );
  }
   Widget _buildControlButton2(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.selectedColor.withValues(alpha: 0.3),
        border: Border(
          top: BorderSide(color: AppTheme.borderColor),
        ),
      ),
      child: ValueListenableBuilder(
        valueListenable: studentsAfter,
        builder: (_, vStudentAfter, child) {
          return Row(
            children: [
              Expanded(
                child: vStudentAfter.isEmpty
                    ? ModernButton(
                      enabled: true,
                        text: 'Thi lại ',
                        icon: Icons.refresh,
                        onPressed:  null ,
                      )
                    : ModernButton(
                          text: 'Thi lại ',
                        icon: Icons.refresh,
                        onPressed:  () {
                          showDialog(context: context,
                           builder: (_){
                            return AlertDialog(
                              title: Text('Xác nhận'),
                              content: Text('Bạn có chắc chắn muốn thi lại không?'),
                              actions: [
                                TextButton(
                                  child: Text('Hủy'),
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                ),
                                TextButton(
                                  child: Text('Xác nhận'),
                                  onPressed: () {
                                  // students.setValue([]);
                                    for (var element in studentsAfter.value) {
                                      element.isDone = false;
                                      element.isVang = false;
                                    }
                                    students.setValue(studentsAfter.value);
                                    studentsAfter.setValue([]);
                                    selectedStudents.value = null;
                                    indexSelect = 0;
                                    Navigator.of(context).pop();
                                  },
                                ),
                              ],
                            );
                          });
                        }   ,
                      ),
              ),
            ],
          );
        },
      ),
    );
  }



  Widget _buildStudentAfterPanel(BuildContext context) {
    return ModernCard(
      padding: const EdgeInsets.all(0),
      child: Column(
        children: [
          SectionHeader(
            title: 'Danh sách thi đợt sau',
           // subtitle: 'Thí sinh đã thi xong',
            action: ValueListenableBuilder(
              valueListenable: studentsAfter,
              builder: (_, vStudentsAfter, __) => StatusBadge.warning(
                '${vStudentsAfter.length} thí sinh',
                icon: Icons.check_circle,
              ),
            ),
          ),
          Expanded(
            child: ValueListenableBuilder(
              valueListenable: studentsAfter,
              builder: (_, vStudentsAfter, child) {
                if (vStudentsAfter.isEmpty) {
                  return EmptyState(
                    icon: Icons.check_circle_outline,
                    title: 'Chưa có thí sinh nào hoàn thành',
                    subtitle: 'Danh sách thí sinh đã thi sẽ hiển thị ở đây',
                  );
                }
    
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: vStudentsAfter.length,
                  itemBuilder: (_, i) {
                    final item = vStudentsAfter[i];
    
                    return Stack(
                      children: [
                        ModernCard(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            children: [
                              Container(
                                width: 40,
                                height: 40,
                                decoration: BoxDecoration(
                                  color: item.isVang
                                      ? AppTheme.warningColor
                                      : AppTheme.successColor,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Center(
                                  child: Text(
                                    item.id,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Thí sinh ${item.id}',
                                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      item.isVang ? 'Vắng mặt' : 'Đã hoàn thành thi',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: AppTheme.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (item.isVang)
                                StatusBadge.warning('Vắng')
                              else
                                StatusBadge.success('Hoàn thành'),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
          ),
          _buildControlButton2(context),
        ],
      ),
    );
  }

  // Helper Methods
  void _startExam() {
    if (students.value.isNotEmpty) {
      indexSelect = 0;
      selectedStudents.value = students.getItem(0);
      // Scroll về đầu danh sách
      _scrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
      _speak(students.getItem(0).text);
    }
  }

  void _nextStudent() {
    if (_hasNextStudent()) {
      indexSelect++;
      final nextStudent = students.getItem(indexSelect);
      selectedStudents.value = nextStudent;

      // Đảm bảo scroll đến student tiếp theo
      Future.delayed(const Duration(milliseconds: 100), () {
        _scrollToStudent(nextStudent);
      });

      _speak(nextStudent.text);
    }
  }

  bool _hasNextStudent() {
    return indexSelect < students.value.length - 1;
  }

  void _scrollToStudent(_Persion student) {
    // Tìm index của student trong danh sách
    final index = students.value.indexWhere((s) => s.id == student.id);
    if (index != -1 && _scrollController.hasClients) {
      // Scroll đến vị trí của student với itemExtent = 80
      final itemHeight = 80.0;
      final targetOffset = index * itemHeight;
      final maxScrollExtent = _scrollController.position.maxScrollExtent;

      // Đảm bảo không scroll quá giới hạn
      final clampedOffset = targetOffset.clamp(0.0, maxScrollExtent);

      _scrollController.animateTo(
        clampedOffset,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeInOutCubic,
      );
      return;
    }

    // Backup method sử dụng GlobalObjectKey
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final context = GlobalObjectKey(student).currentContext;
      if (context != null) {
        Scrollable.ensureVisible(
          context,
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeInOutCubic,
          alignment: 0.1,
        );
      }
    });
  }

  void _markStudentAbsent(_Persion student) {
    print('_markStudentAbsent called for student: ${student.text} (ID: ${student.id})');

    final newStudent = student.copyWith(isVang: true);
    studentsAfter.add(newStudent);
    final idxStudent = students.value.indexWhere((e) => e.id == student.id);
    if (idxStudent != -1) {
      students.updateValuebyIndex(
        idxStudent,
        student.copyWith(isVang: true),
      );
      print('Student marked as absent successfully');
    } else {
      print('Student not found in list');
    }
 //   _nextStudent();
  }

  void _removeSelectedStudent() {
    final selectedStudent = selectedStudents.value;
    if (selectedStudent != null && _removingStudentId == null) {
      // Đánh dấu student đang được xóa để hiển thị animation
      setState(() {
        _removingStudentId = selectedStudent.id;
      });

      // Bắt đầu animation
      _removeAnimationController.forward().then((_) {
        // Sau khi animation hoàn thành, xóa student khỏi danh sách
        final idxStudent = students.value.indexWhere((e) => e.id == selectedStudent.id);
        if (idxStudent != -1) {
          students.removeIndex(idxStudent);

          // Cập nhật selectedStudents và indexSelect
          if (students.value.isEmpty) {
            selectedStudents.value = null;
            indexSelect = 0;
          } else {
            // Nếu xóa student cuối cùng, chọn student trước đó
            if (indexSelect >= students.value.length) {
              indexSelect = students.value.length - 1;
            }
            if(idxStudent == students.value.length){
                selectedStudents.value = null;
            }else{
               selectedStudents.value = students.getItem(idxStudent);
            }
           
          }
        }

        // Reset animation và removingStudentId
        setState(() {
          _removingStudentId = null;
        });
        _removeAnimationController.reset();
      });
    }
  }

}

class _Persion {
  String id;
  String text;
  bool isDone = false;
  bool isVang = false;
  _Persion(this.id, this.text, this.isDone, this.isVang);

  _Persion copyWith({
    String? id,
    String? text,
    bool? isDone,
    bool? isVang,
  }) {
    return _Persion(
      id ?? this.id,
      text ?? this.text,
      isDone ?? this.isDone,
      isVang ?? this.isVang,
    );
  }
}
