import 'dart:io';

import 'package:animations/animations.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tts_truong_lai/theme/app_theme.dart';
import 'package:tts_truong_lai/tts_truong_lai_body.dart';
import 'package:tts_truong_lai/widgets/custom_widgets.dart';

class TtsTruongLaiNhapSoLuong extends StatefulWidget {
  const TtsTruongLaiNhapSoLuong({super.key});

  @override
  State<TtsTruongLaiNhapSoLuong> createState() => _TtsTruongLaiNhapSoLuongState();
}

class _TtsTruongLaiNhapSoLuongState extends State<TtsTruongLaiNhapSoLuong> {
  late TextEditingController controller;

  // Slider values (0.0 to 1.5)
  ValueNotifier<double> slider1Value = ValueNotifier(0.81);
  ValueNotifier<double> slider2Value = ValueNotifier(0.97);

  // TextField controllers
  late TextEditingController textField1Controller;
  late TextEditingController textField2Controller;
  late FlutterTts flutterTts;

  @override
  void initState() {
    controller = TextEditingController(text: '');
    textField1Controller = TextEditingController();
    textField2Controller = TextEditingController();
    _loadSettings();
    initTts();
    super.initState();
  }
    TtsState ttsState = TtsState.stopped;

  bool get isPlaying => ttsState == TtsState.playing;
  bool get isStopped => ttsState == TtsState.stopped;
  bool get isPaused => ttsState == TtsState.paused;
  bool get isContinued => ttsState == TtsState.continued;

  bool get isIOS => !kIsWeb && Platform.isIOS;
  bool get isAndroid => !kIsWeb && Platform.isAndroid;
  bool get isWindows => !kIsWeb && Platform.isWindows;
  bool get isWeb => kIsWeb;
  dynamic initTts()async {
    flutterTts = FlutterTts();
  
    await flutterTts.setLanguage("vi-VN");

    _setAwaitOptions();

    if (isAndroid) {
      _getDefaultEngine();
      _getDefaultVoice();
    }


    flutterTts.setContinueHandler(() {
      setState(() {
        ttsState = TtsState.continued;
      });
    });

    flutterTts.setErrorHandler((msg) {
      setState(() {
        ttsState = TtsState.stopped;
      });
    });
  }
  Future<void> _setAwaitOptions() async {
    await flutterTts.awaitSpeakCompletion(true);
  }
  Future<void> _getDefaultEngine() async {
    var engine = await flutterTts.getDefaultEngine;
    if (engine != null) {
    }
  }

  Future<void> _getDefaultVoice() async {
    var voice = await flutterTts.getDefaultVoice;
    if (voice != null) {
    }
  }
   Future<void> _speak(final String text) async {
    await flutterTts.setVolume(1);
    await flutterTts.setSpeechRate(slider1Value.value);
    await flutterTts.setPitch(slider2Value.value);
    await flutterTts.speak(text);
     
  }

  @override
  void dispose() {
    controller.dispose();
    textField1Controller.dispose();
    textField2Controller.dispose();
    super.dispose();
  }

  // Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
      slider1Value.value = prefs.getDouble('slider1_value') ?? 0.81;
      slider2Value.value = prefs.getDouble('slider2_value') ?? 0.97;
      textField1Controller.text = prefs.getString('textfield1_value') ?? 'Xin mời thí sinh';
      textField2Controller.text = prefs.getString('textfield2_value') ?? 'chuẩn bị';
  }

  // Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('slider1_value', slider1Value.value);
    await prefs.setDouble('slider2_value', slider2Value.value);
    await prefs.setString('textfield1_value', textField1Controller.text);
    await prefs.setString('textfield2_value', textField2Controller.text);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cài đặt đã được lưu thành công!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
  @override
  Widget build(final BuildContext context) {
    return Scaffold(
         appBar: AppBar(
        actions: [
          IconButton(
            icon: Icon(Icons.settings),
            onPressed: () {
              showModal(context: context,
               builder: (builderContext){
                return AlertDialog(
                  title: const Text('Cài đặt'),
                  content: SizedBox(
                    width: 500,
                    height: 400,
                    child: Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                  TextButton(onPressed: ()async{
                                    _speak('${textField1Controller.text.isEmpty ?
                                     'Xin mời thí sinh ' 
                                     : textField1Controller.text}0 1${textField2Controller.text.isEmpty ? 'chuẩn bị' : textField2Controller.text}');
                                  }, child: Text('Nghe thử'))
                              ],
                            ),
                            // Slider 1
                            ValueListenableBuilder(
                              valueListenable: slider1Value,
                              builder: (context, value, child) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Tốc độ đọc: ${value.toStringAsFixed(2)}',
                                      style: Theme.of(context).textTheme.titleMedium,
                                    ),
                                      Slider(
                                        value: value,
                                        min: 0.0,
                                        max: 1.5,
                                        divisions: 150,
                                        onChanged: (value) {
                                           slider1Value.value = value;
                                        },
                                      ),
                                  ],
                                );
                              }
                            ),
                          
                            const SizedBox(height: 16),

                            // Slider 2
                            ValueListenableBuilder(
                              valueListenable: slider2Value,
                              builder: (context, value, child) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'tone giọng: ${value.toStringAsFixed(2)}',
                                      style: Theme.of(context).textTheme.titleMedium,
                                    ),
                                       Slider(
                              value: value,
                              min: 0.0,
                              max: 1.5,
                              divisions: 150,
                              onChanged: (value) {
                                slider2Value.value = value;
                              },
                            ),
                                  ],
                                );
                              }
                            ),
                         
                            const SizedBox(height: 16),

                            // TextFields
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: textField1Controller,
                                    decoration: const InputDecoration(
                                      labelText: 'Lời thoại trước số báo danh',
                                      border: OutlineInputBorder(),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextField(
                                    controller: textField2Controller,
                                    decoration: const InputDecoration(
                                      labelText: 'Lời thoại sau số báo danh',
                                      border: OutlineInputBorder(),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Complete button
                            Center(
                              child: ElevatedButton.icon(
                                onPressed: () async {
                                  final navigator = Navigator.of(context);
                                  await _saveSettings();
                                  if (mounted) {
                                    navigator.pop();
                                  }
                                },
                                icon: const Icon(Icons.check),
                                label: const Text('Hoàn thành'),
                                style: ElevatedButton.styleFrom(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 24,
                                    vertical: 12,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      child: const Text('Đóng'),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                    ),
                  ],
                );
              });
            }
          ),
      
        ],
      ),

      body: Center(
        child:  _buildHeaderSection(context),
      ),
    );
  }

   Widget _buildHeaderSection(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 800;

    return ModernCard(
      padding: const EdgeInsets.all(24),
      child: isSmallScreen
          ? Column(
            mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Thiết lập số lượng thí sinh',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'Nhập tổng số thí sinh dự thi để bắt đầu',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondary,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(child: _buildInputNumber()),
                    const SizedBox(width: 16),
                    ModernButton(
                      text: 'Xác nhận',
                      icon: Icons.check,
                      onPressed: () => _showConfirmationDialog(context),
                    ),
                  ],
                ),
              ],
            )
          : Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Thiết lập số lượng thí sinh',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Nhập tổng số thí sinh dự thi để bắt đầu',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 24),
                SizedBox(
                  width: 200,
                  child: _buildInputNumber(),
                ),
                const SizedBox(width: 16),
                ModernButton(
                  text: 'Xác nhận',
                  icon: Icons.check,
                  onPressed: () => _showConfirmationDialog(context),
                ),
              ],
            ),
    );
  }

  Widget _buildInputNumber() {
    return ValueListenableBuilder(
      valueListenable: controller,
      builder: (context, value, child) {
        return TextField(
          controller: controller,
          keyboardType: TextInputType.number,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          decoration: InputDecoration(
            labelText: 'Số lượng thí sinh',
            hintText: 'Nhập số lượng...',
            prefixIcon: Icon(Icons.people),
            suffixIcon: controller.text.isNotEmpty
                ? IconButton(
                    icon: Icon(Icons.clear),
                    onPressed: () {
                      controller.clear();
                    },
                  )
                : null,
          ),
        );
      },
    );
  }

    void _showConfirmationDialog(BuildContext context) {
    final total = int.tryParse(controller.text) ?? 0;
    if (total <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Vui lòng nhập số lượng thí sinh hợp lệ'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.group, color: AppTheme.primaryColor),
            const SizedBox(width: 12),
            Text('Xác nhận số lượng thí sinh'),
          ],
        ),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Bạn có chắc chắn muốn tạo danh sách với:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.selectedColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.people, color: AppTheme.primaryColor),
                    const SizedBox(width: 12),
                    Text(
                      '$total thí sinh',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          ModernButton(
            text: 'Hủy',
            isSecondary: true,
            onPressed: () => Navigator.of(context).pop(),
          ),
          ModernButton(
            text: 'Xác nhận',
            icon: Icons.check,
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => TTSTruongLaiBody(total:total),
                ),
              );
            
            
            },
          ),
        ],
      ),
    );
  }
}