import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shimmer/shimmer.dart';
import '../theme/app_theme.dart';

// Custom Card Widget with hover effects
class ModernCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? backgroundColor;
  final bool isSelected;
  final bool isHoverable;
  
  const ModernCard({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.margin,
    this.backgroundColor,
    this.isSelected = false,
    this.isHoverable = true,
  });

  @override
  State<ModernCard> createState() => _ModernCardState();
}

class _ModernCardState extends State<ModernCard> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      margin: widget.margin ?? const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      decoration: BoxDecoration(
        color: widget.isSelected 
            ? Colors.white 
            : widget.backgroundColor ?? AppTheme.cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: widget.isSelected 
              ? AppTheme.primaryColor 
              : AppTheme.borderColor,
          width: widget.isSelected ? 2 : 1,
        ),
        boxShadow: _isHovered && widget.isHoverable
            ? [BoxShadow(
                color: Colors.black.withValues(alpha:  0.1),
                blurRadius: 20,
                offset: const Offset(0, 4),
              )]
            : [BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              )],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onTap,
          onHover: widget.isHoverable ? (hovering) {
            setState(() {
              _isHovered = hovering;
            });
          } : null,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: widget.padding ?? const EdgeInsets.all(16),
            child: widget.child,
          ),
        ),
      ),
    ).animate(target: _isHovered ? 1 : 0)
     .scale(begin: const Offset(1, 1), end: const Offset(1.02, 1.02));
  }
}

// Custom Button with loading state
class ModernButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isSecondary;
  final bool isDestructive;
  final IconData? icon;
  final Size? size;
  final bool enabled;
  
  const ModernButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isSecondary = false,
    this.isDestructive = false,
    this.icon,
    this.size,
    this.enabled = false,
  });

  @override
  Widget build(BuildContext context) {
    Color backgroundColor;
    Color foregroundColor;
    
    if (isDestructive) {
      backgroundColor = AppTheme.errorColor;
      foregroundColor = Colors.white;
    } else if (isSecondary) {
      backgroundColor = Colors.transparent;
      foregroundColor = AppTheme.primaryColor;
    } else {
      backgroundColor = AppTheme.primaryColor;
      foregroundColor = Colors.white;
    }
    
    return SizedBox(
      width: size?.width,
      height: size?.height ?? 44,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: enabled? Colors.black54: backgroundColor,
          foregroundColor: enabled? Colors.grey: foregroundColor,
          side: isSecondary ? const BorderSide(color: AppTheme.primaryColor) : null,
          elevation: isSecondary ? 0 : 2,
        ),
        child: isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (icon != null) ...[
                    Icon(icon, size: 16),
                    const SizedBox(width: 6),
                  ],
                  Flexible(
                    child: Text(
                      text,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

// Status Badge Widget
class StatusBadge extends StatelessWidget {
  final String text;
  final Color backgroundColor;
  final Color textColor;
  final IconData? icon;
  
  const StatusBadge({
    super.key,
    required this.text,
    required this.backgroundColor,
    required this.textColor,
    this.icon,
  });
  
  factory StatusBadge.success(String text, {IconData? icon}) {
    return StatusBadge(
      text: text,
      backgroundColor: AppTheme.successColor.withValues(alpha: 0.1),
      textColor: AppTheme.successColor,
      icon: icon ?? Icons.check_circle,
    );
  }
  
  factory StatusBadge.warning(String text, {IconData? icon}) {
    return StatusBadge(
      text: text,
      backgroundColor: AppTheme.warningColor.withValues(alpha: 0.1),
      textColor: AppTheme.warningColor,
      icon: icon ?? Icons.warning,
    );
  }
  
  factory StatusBadge.error(String text, {IconData? icon}) {
    return StatusBadge(
      text: text,
      backgroundColor: AppTheme.errorColor.withValues(alpha: 0.1),
      textColor: AppTheme.errorColor,
      icon: icon ?? Icons.error,
    );
  }
  
  factory StatusBadge.info(String text, {IconData? icon}) {
    return StatusBadge(
      text: text,
      backgroundColor: AppTheme.infoColor.withValues(alpha: 0.1),
      textColor: AppTheme.infoColor,
      icon: icon ?? Icons.info,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 12, color: textColor),
            const SizedBox(width: 4),
          ],
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }
}

// Loading Shimmer Widget
class LoadingShimmer extends StatelessWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  
  const LoadingShimmer({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppTheme.borderColor,
      highlightColor: AppTheme.hoverColor,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: AppTheme.borderColor,
          borderRadius: borderRadius ?? BorderRadius.circular(8),
        ),
      ),
    );
  }
}

// Section Header Widget
class SectionHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? action;
  
  const SectionHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.headlineSmall,
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodySmall,
                  ),
                ],
              ],
            ),
          ),
          if (action != null) action!,
        ],
      ),
    );
  }
}

// Empty State Widget
class EmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Widget? action;
  
  const EmptyState({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 64,
              color: AppTheme.textTertiary,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: AppTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}
